# 🏗️ Kritrima AI CLI - Architecture Overview

This document provides a comprehensive overview of the Kritrima AI CLI system architecture, design patterns, and implementation details.

## 🎯 System Overview

Kritrima AI CLI is a production-ready, agentic AI-powered command-line interface that brings autonomous AI capabilities to local development environments. The system is designed with enterprise-grade architecture principles, focusing on safety, reliability, and extensibility.

### Core Principles

1. **Safety First** - Multi-layered safety controls with risk assessment
2. **Universal Compatibility** - Single SDK approach for multiple AI providers
3. **Autonomous Operation** - Intelligent tool chaining and execution
4. **Context Awareness** - Deep project understanding and analysis
5. **Production Ready** - Enterprise-grade error handling and monitoring

## 🏛️ High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        User Interface Layer                     │
├─────────────────────────────────────────────────────────────────┤
│  CLI Interface  │  Terminal UI  │  Session Management  │  Config │
└─────────────────┴───────────────┴──────────────────────┴─────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                      Agent Engine Layer                        │
├─────────────────────────────────────────────────────────────────┤
│  Agent Engine   │  Conversation Loop  │  Stream Processor      │
└─────────────────┴─────────────────────┴────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                      Provider Layer                            │
├─────────────────────────────────────────────────────────────────┤
│  Universal Provider (OpenAI SDK)                               │
│  ├── OpenAI      ├── Deepseek    ├── Ollama    ├── Azure      │
└─────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                        Tool Layer                              │
├─────────────────────────────────────────────────────────────────┤
│  Tool Registry  │  Shell Tool  │  File Tool  │  Context Tool   │
└─────────────────┴──────────────┴─────────────┴─────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                       Safety Layer                             │
├─────────────────────────────────────────────────────────────────┤
│  Approval System  │  Command Analyzer  │  Risk Assessment     │
└───────────────────┴────────────────────┴──────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                      Storage Layer                             │
├─────────────────────────────────────────────────────────────────┤
│  Session Storage  │  Context Cache  │  Configuration  │  Logs  │
└───────────────────┴─────────────────┴─────────────────┴────────┘
```

## 📦 Component Architecture

### 1. User Interface Layer

#### CLI Interface (`src/index.js`)
- **Purpose**: Main entry point and command parsing
- **Responsibilities**:
  - Command-line argument parsing
  - Route commands to appropriate handlers
  - Global error handling
  - Application lifecycle management

#### Terminal UI (`src/ui/TerminalUI.js`)
- **Purpose**: Rich terminal user interface
- **Responsibilities**:
  - User input/output handling
  - Real-time streaming display
  - Progress indicators and status updates
  - Interactive prompts and confirmations

#### Session Management (`src/storage/SessionManager.js`)
- **Purpose**: Conversation persistence and management
- **Responsibilities**:
  - Session creation and storage
  - Message history management
  - Session search and export
  - Cleanup and retention policies

### 2. Agent Engine Layer

#### Agent Engine (`src/agent/AgentEngine.js`)
- **Purpose**: Core orchestration and coordination
- **Responsibilities**:
  - Component initialization and lifecycle
  - Context management and updates
  - Health monitoring and diagnostics
  - Configuration management

#### Conversation Loop (`src/agent/ConversationLoop.js`)
- **Purpose**: Manages conversation flow and tool execution
- **Responsibilities**:
  - Message processing and routing
  - Tool call parsing and execution
  - Streaming response handling
  - Special command processing

#### Stream Processor (`src/utils/StreamProcessor.js`)
- **Purpose**: Real-time response processing
- **Responsibilities**:
  - Streaming chunk processing
  - Tool call extraction and validation
  - Provider-specific format handling
  - Error detection and recovery

### 3. Provider Layer

#### Universal Provider (`src/providers/UniversalProvider.js`)
- **Purpose**: Unified AI provider interface
- **Key Innovation**: Single OpenAI SDK for all providers
- **Responsibilities**:
  - Provider-specific configuration
  - Request/response transformation
  - Error handling and retry logic
  - Capability detection and adaptation

**Supported Providers**:
- **OpenAI**: Native support with full feature set
- **Deepseek**: OpenAI-compatible API with custom base URL
- **Ollama**: Local LLM execution with API compatibility
- **Azure OpenAI**: Enterprise deployment with custom headers
- **Custom**: Any OpenAI-compatible endpoint

### 4. Tool Layer

#### Tool Registry (`src/tools/ToolRegistry.js`)
- **Purpose**: Tool discovery, registration, and execution
- **Responsibilities**:
  - Tool lifecycle management
  - Schema validation and formatting
  - Provider-specific tool adaptation
  - Usage statistics and monitoring

#### Shell Tool (`src/tools/ShellTool.js`)
- **Purpose**: Cross-platform shell command execution
- **Capabilities**:
  - Windows WSL, macOS, and Linux support
  - Command validation and filtering
  - Output capture and streaming
  - Timeout and resource limits

#### File Tool (`src/tools/FileTool.js`)
- **Purpose**: Comprehensive file system operations
- **Capabilities**:
  - Read, write, search, and manipulation
  - Glob pattern matching
  - Backup and versioning
  - Permission and security controls

#### Context Tool (`src/tools/ContextTool.js`)
- **Purpose**: Intelligent project analysis
- **Capabilities**:
  - Project type detection
  - Dependency analysis
  - File structure mapping
  - Technology stack identification

### 5. Safety Layer

#### Approval System (`src/safety/ApprovalSystem.js`)
- **Purpose**: Multi-mode safety control system
- **Modes**:
  - **Suggest**: Always ask for approval
  - **Auto-Edit**: Auto-approve file operations
  - **Full-Auto**: Risk-based auto-approval
- **Features**:
  - Interactive approval prompts
  - Risk threshold configuration
  - Approval history and analytics

#### Command Analyzer (`src/safety/CommandAnalyzer.js`)
- **Purpose**: Advanced command risk assessment
- **Capabilities**:
  - Pattern-based risk scoring
  - Security vulnerability detection
  - Syntax validation
  - Safe alternative suggestions

### 6. Storage Layer

#### Configuration Management (`src/config/ConfigManager.js`)
- **Purpose**: Flexible configuration system
- **Features**:
  - YAML-based configuration
  - Interactive setup wizard
  - Environment variable support
  - Validation and migration

#### Project Analyzer (`src/context/ProjectAnalyzer.js`)
- **Purpose**: Deep project understanding
- **Capabilities**:
  - File system traversal
  - Dependency extraction
  - Caching and optimization
  - Insight generation

## 🔄 Data Flow Architecture

### 1. User Input Processing

```
User Input → CLI Parser → Command Router → Agent Engine
                                              ↓
Context Discovery ← Project Analyzer ← Agent Engine
                                              ↓
Message Preparation → Conversation Loop → Provider
```

### 2. AI Response Processing

```
Provider Response → Stream Processor → Tool Call Parser
                                              ↓
Tool Registry ← Tool Selection ← Approval System
                                              ↓
Tool Execution → Result Processing → UI Display
```

### 3. Safety Control Flow

```
Tool Request → Risk Analysis → Approval Decision
                    ↓               ↓
Command Analyzer → Risk Score → User Prompt/Auto-Approve
                                              ↓
Execution → Monitoring → Audit Log → Session Storage
```

## 🛡️ Security Architecture

### Multi-Layered Security

1. **Input Validation**
   - Command syntax validation
   - Parameter sanitization
   - Path traversal prevention

2. **Risk Assessment**
   - Pattern-based analysis
   - Privilege escalation detection
   - Resource usage monitoring

3. **Execution Control**
   - Sandboxed execution environments
   - Resource limits and timeouts
   - Output size restrictions

4. **Audit and Monitoring**
   - Complete operation logging
   - Error tracking and analysis
   - Usage pattern detection

### Safety Mechanisms

- **Command Filtering**: Configurable allow/block lists
- **Risk Scoring**: Dynamic risk assessment (0.0-1.0 scale)
- **Approval Gates**: Multi-mode approval system
- **Execution Limits**: Timeout, memory, and output constraints
- **Rollback Capability**: Automatic backup and recovery

## 🔧 Design Patterns

### 1. Strategy Pattern
- **Provider Selection**: Dynamic AI provider switching
- **Tool Execution**: Pluggable tool implementations
- **Safety Modes**: Configurable approval strategies

### 2. Observer Pattern
- **Event Handling**: Component communication
- **Progress Tracking**: Real-time status updates
- **Error Propagation**: Centralized error handling

### 3. Factory Pattern
- **Tool Creation**: Dynamic tool instantiation
- **Provider Initialization**: Configuration-based setup
- **Session Management**: Session lifecycle control

### 4. Command Pattern
- **Tool Execution**: Encapsulated operations
- **Undo/Redo**: Operation history and rollback
- **Batch Processing**: Queued command execution

## 📊 Performance Architecture

### Optimization Strategies

1. **Caching**
   - Project analysis results
   - Configuration data
   - Provider responses (when appropriate)

2. **Streaming**
   - Real-time AI response processing
   - Progressive tool execution
   - Incremental output display

3. **Lazy Loading**
   - On-demand tool initialization
   - Deferred context analysis
   - Progressive file discovery

4. **Resource Management**
   - Connection pooling
   - Memory usage monitoring
   - Garbage collection optimization

### Scalability Considerations

- **Horizontal Scaling**: Multi-session support
- **Vertical Scaling**: Resource-aware execution
- **Load Balancing**: Provider failover and rotation
- **Rate Limiting**: API usage optimization

## 🔮 Extensibility Architecture

### Plugin System

The architecture supports easy extension through:

1. **Custom Tools**: Implement `BaseTool` interface
2. **Custom Providers**: Extend `UniversalProvider`
3. **Custom Safety Rules**: Add to `CommandAnalyzer`
4. **Custom UI Components**: Extend `TerminalUI`

### Integration Points

- **CI/CD Pipelines**: Automated code review and assistance
- **IDE Extensions**: Direct integration with development environments
- **API Endpoints**: RESTful API for external integrations
- **Webhook Support**: Event-driven automation

## 🚀 Deployment Architecture

### Local Development
- Direct Node.js execution
- Development mode with hot reload
- Debug logging and profiling

### Production Deployment
- Global npm package installation
- Systemd service integration
- Docker containerization support

### Enterprise Deployment
- Centralized configuration management
- LDAP/SSO integration
- Audit log aggregation
- Policy enforcement

## 📈 Monitoring and Observability

### Metrics Collection
- **Performance Metrics**: Response times, throughput
- **Usage Analytics**: Command frequency, tool usage
- **Error Rates**: Failure patterns and recovery
- **Resource Utilization**: Memory, CPU, network

### Health Monitoring
- **Component Health**: Individual service status
- **Provider Connectivity**: API availability and latency
- **Tool Functionality**: Execution success rates
- **Configuration Validity**: Settings verification

### Alerting and Notifications
- **Error Thresholds**: Automatic issue detection
- **Performance Degradation**: Proactive monitoring
- **Security Events**: Suspicious activity alerts
- **Maintenance Windows**: Scheduled downtime

This architecture provides a solid foundation for a production-ready AI CLI system that can scale, adapt, and evolve with changing requirements while maintaining the highest standards of safety and reliability.
