/**
 * ToolRegistry - Manages and provides access to all available tools
 * Handles tool registration, discovery, and execution coordination
 */

import { ShellTool } from './ShellTool.js';
import { FileTool } from './FileTool.js';
import { ContextTool } from './ContextTool.js';

export class ToolRegistry {
  constructor(config = {}) {
    this.config = config;
    this.tools = new Map();
    this.toolSchemas = new Map();
    
    // Initialize built-in tools
    this.initializeTools();
  }

  /**
   * Initialize all available tools
   */
  initializeTools() {
    // Shell command execution tool
    const shellTool = new ShellTool(this.config.shell || {});
    this.registerTool('shell', shellTool);

    // File operations tool
    const fileTool = new FileTool(this.config.file || {});
    this.registerTool('file_operations', fileTool);

    // Context analysis tool
    const contextTool = new ContextTool(this.config.context || {});
    this.registerTool('analyze_context', contextTool);
  }

  /**
   * Register a tool with the registry
   */
  registerTool(name, tool) {
    this.tools.set(name, tool);
    this.toolSchemas.set(name, tool.getSchema());
  }

  /**
   * Get a specific tool by name
   */
  getTool(name) {
    const tool = this.tools.get(name);
    if (!tool) {
      throw new Error(`Tool not found: ${name}`);
    }
    return tool;
  }

  /**
   * Get all available tools
   */
  getAvailableTools(context = null) {
    const tools = [];
    
    for (const [name, tool] of this.tools) {
      if (tool.isAvailable(context)) {
        tools.push({
          name,
          description: tool.getDescription(),
          schema: tool.getSchema(),
          capabilities: tool.getCapabilities()
        });
      }
    }
    
    return tools;
  }

  /**
   * Get tools formatted for specific AI provider
   */
  getToolsForProvider(providerType) {
    const tools = [];
    
    for (const [name, schema] of this.toolSchemas) {
      const tool = this.tools.get(name);
      
      if (tool.isAvailable()) {
        tools.push(this.formatToolForProvider(name, schema, providerType));
      }
    }
    
    return tools;
  }

  /**
   * Format tool schema for specific provider
   */
  formatToolForProvider(name, schema, providerType) {
    switch (providerType) {
      case 'openai':
      case 'deepseek':
      case 'azure':
        return {
          type: 'function',
          function: {
            name: name,
            description: schema.description,
            parameters: schema.parameters
          }
        };
        
      case 'ollama':
        // Ollama might have different format requirements
        return {
          name: name,
          description: schema.description,
          parameters: schema.parameters
        };
        
      default:
        return {
          type: 'function',
          function: {
            name: name,
            description: schema.description,
            parameters: schema.parameters
          }
        };
    }
  }

  /**
   * Execute a tool with given arguments
   */
  async executeTool(name, args, context = null) {
    const tool = this.getTool(name);

    // Validate arguments against schema
    this.validateArguments(name, args);

    // Execute the tool
    return await tool.execute(args, context);
  }

  /**
   * Validate tool arguments against schema
   */
  validateArguments(toolName, args) {
    const schema = this.toolSchemas.get(toolName);
    if (!schema) {
      throw new Error(`No schema found for tool: ${toolName}`);
    }

    // Basic validation - could be enhanced with a proper JSON schema validator
    const required = schema.parameters.required || [];

    for (const requiredParam of required) {
      if (!(requiredParam in args)) {
        throw new Error(`Missing required parameter: ${requiredParam} for tool: ${toolName}`);
      }
    }
  }

  /**
   * Get tool usage statistics
   */
  getUsageStats() {
    const stats = {};
    
    for (const [name, tool] of this.tools) {
      stats[name] = tool.getUsageStats();
    }
    
    return stats;
  }

  /**
   * Health check for all tools
   */
  async healthCheck() {
    const results = {};
    
    for (const [name, tool] of this.tools) {
      try {
        results[name] = await tool.healthCheck();
      } catch (error) {
        results[name] = {
          status: 'unhealthy',
          error: error.message
        };
      }
    }
    
    const allHealthy = Object.values(results).every(result => result.status === 'healthy');
    
    return {
      status: allHealthy ? 'healthy' : 'degraded',
      tools: results
    };
  }

  /**
   * Enable or disable a tool
   */
  setToolEnabled(name, enabled) {
    const tool = this.getTool(name);
    tool.setEnabled(enabled);
  }

  /**
   * Get tool by category
   */
  getToolsByCategory(category) {
    const tools = [];
    
    for (const [name, tool] of this.tools) {
      if (tool.getCategory() === category) {
        tools.push({
          name,
          tool,
          schema: this.toolSchemas.get(name)
        });
      }
    }
    
    return tools;
  }

  /**
   * Search tools by capability
   */
  searchToolsByCapability(capability) {
    const tools = [];
    
    for (const [name, tool] of this.tools) {
      const capabilities = tool.getCapabilities();
      if (capabilities.includes(capability)) {
        tools.push({
          name,
          tool,
          schema: this.toolSchemas.get(name)
        });
      }
    }
    
    return tools;
  }

  /**
   * Get tool documentation
   */
  getToolDocumentation(name) {
    const tool = this.getTool(name);
    const schema = this.toolSchemas.get(name);
    
    return {
      name,
      description: tool.getDescription(),
      category: tool.getCategory(),
      capabilities: tool.getCapabilities(),
      schema,
      examples: tool.getExamples(),
      usage: tool.getUsageInstructions()
    };
  }

  /**
   * Cleanup all tools
   */
  async cleanup() {
    for (const [name, tool] of this.tools) {
      try {
        await tool.cleanup();
      } catch (error) {
        console.error(`Error cleaning up tool ${name}:`, error);
      }
    }
  }

  /**
   * Reload tools configuration
   */
  async reloadConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    
    // Reinitialize tools with new configuration
    for (const [name, tool] of this.tools) {
      const toolConfig = this.config[name] || {};
      await tool.updateConfig(toolConfig);
    }
  }
}
