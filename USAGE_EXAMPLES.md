# 🚀 Kritrima AI CLI - Usage Examples

This document provides comprehensive examples of how to use the Kritrima AI CLI system.

## 🏁 Getting Started

### 1. Initial Setup

```bash
# Install dependencies
npm install

# Initialize configuration
node src/index.js config --init

# Check system health
node src/index.js health
```

### 2. Basic Commands

```bash
# Show help
node src/index.js --help

# Show current configuration
node src/index.js config --show

# Analyze current project
node src/index.js analyze
```

## 💬 Interactive Chat Mode

### Starting a Session

```bash
# Start interactive mode
node src/index.js chat

# Or use the shorthand
node src/index.js
```

### Example Conversation

```
🤖 Kritrima AI CLI
Production-ready agentic AI-powered CLI tool system

Features:
• Autonomous shell command execution
• Comprehensive file operations
• Intelligent context awareness
• Multi-provider AI support (OpenAI, Deepseek, Ollama)
• Advanced safety controls

ℹ️  Started new session: abc123
✅ Context loaded: 15 files, 5 dependencies
ℹ️  Type your message or command. Use /help for available commands, /exit to quit.

> Create a simple Node.js HTTP server

🤖 I'll help you create a simple Node.js HTTP server. Let me create a basic server file for you.

🔧 Tool: file_operations
✅ Success

I've created a simple HTTP server in `server.js`. Here's what it does:

1. Creates an HTTP server that listens on port 3000
2. Responds with "Hello World!" to all requests
3. Logs when the server starts

Would you like me to start the server for you?

> Yes, start the server

🔍 Tool Execution Request:
Tool: shell
Command: node server.js
Risk Level: 🟢 Low

Do you want to proceed? (Use arrow keys)
❯ Approve
  Deny
  View Details

> Approve

🔧 Tool: shell
✅ Success
Output: Server running at http://localhost:3000/

The server is now running! You can test it by opening http://localhost:3000 in your browser or using curl.

> /exit
Goodbye!
```

## 🔧 Single Command Mode

### File Operations

```bash
# Create a file
node src/index.js chat "Create a README.md file for this project"

# Analyze code quality
node src/index.js chat "Analyze the code quality and suggest improvements"

# Fix build errors
node src/index.js chat "Check for and fix any build errors in this project"
```

### Development Tasks

```bash
# Set up a new React project
node src/index.js chat "Set up a new React project with TypeScript and testing"

# Add dependencies
node src/index.js chat "Add Express.js and set up a basic API server"

# Run tests
node src/index.js chat "Run the test suite and show me the results"
```

### System Administration

```bash
# Check system resources
node src/index.js chat "Check system resources and disk usage"

# Update packages
node src/index.js chat "Update all npm packages to their latest versions"

# Clean up temporary files
node src/index.js chat "Clean up temporary files and caches"
```

## ⚙️ Configuration Examples

### Provider Configuration

#### OpenAI Setup
```bash
node src/index.js config --set provider.type=openai
node src/index.js config --set provider.apiKey=sk-your-key-here
node src/index.js config --set provider.model=gpt-4-turbo-preview
```

#### Deepseek Setup
```bash
node src/index.js config --set provider.type=deepseek
node src/index.js config --set provider.apiKey=your-deepseek-key
node src/index.js config --set provider.model=deepseek-chat
node src/index.js config --set provider.baseURL=https://api.deepseek.com/v1
```

#### Ollama Setup (Local)
```bash
node src/index.js config --set provider.type=ollama
node src/index.js config --set provider.model=llama2
node src/index.js config --set provider.baseURL=http://localhost:11434/v1
```

### Safety Configuration

```bash
# Set approval mode
node src/index.js config --set safety.mode=suggest      # Always ask
node src/index.js config --set safety.mode=auto-edit    # Auto-approve file ops
node src/index.js config --set safety.mode=full-auto    # Auto-approve based on risk

# Set risk threshold
node src/index.js config --set safety.riskThreshold=0.5  # More permissive
node src/index.js config --set safety.riskThreshold=0.9  # More restrictive
```

## 📊 Session Management

### Working with Sessions

```bash
# List all sessions
node src/index.js sessions --list

# Show session details
node src/index.js sessions --show abc123

# Resume a specific session
node src/index.js chat --session abc123

# Delete a session
node src/index.js sessions --delete abc123

# Clear all sessions
node src/index.js sessions --clear
```

### Session Export

```bash
# Export session as JSON
node src/index.js sessions --export abc123 --format json > session.json

# Export as Markdown
node src/index.js sessions --export abc123 --format markdown > session.md
```

## 🔍 Project Analysis

### Basic Analysis

```bash
# Analyze current directory
node src/index.js analyze

# Deep analysis with file content
node src/index.js analyze --deep

# Output as JSON
node src/index.js analyze --output json > analysis.json

# Analyze specific directory
node src/index.js analyze /path/to/project
```

### Analysis Output Example

```json
{
  "workingDirectory": "/path/to/project",
  "projectType": "Node.js",
  "files": [
    {
      "path": "package.json",
      "name": "package.json",
      "extension": "json",
      "size": 1024,
      "modified": "2024-01-15T10:30:00.000Z"
    }
  ],
  "dependencies": ["express", "lodash", "axios"],
  "packageManagers": ["npm"],
  "stats": {
    "totalFiles": 25,
    "totalSize": 102400,
    "fileTypes": {
      "js": 15,
      "json": 3,
      "md": 2
    }
  }
}
```

## 🛡️ Safety Features

### Approval Modes

#### Suggest Mode (Default)
```bash
> Delete all log files

🔍 Tool Execution Request:
Tool: shell
Command: find . -name "*.log" -delete
Risk Level: 🟡 Medium

Risk Factors:
  - Uses find command with delete
  - Recursive operation with potentially dangerous command

Do you want to proceed?
❯ Approve
  Deny
  View Details
```

#### Auto-Edit Mode
```bash
# File operations are auto-approved
> Create a new component file

🔧 Tool: file_operations
✅ Success (Auto-approved: Low risk file operation)

# Shell commands still require approval
> Install new packages

🔍 Tool Execution Request:
Tool: shell
Command: npm install express
Risk Level: 🟡 Medium
```

#### Full-Auto Mode
```bash
# Low and medium risk operations are auto-approved
> List files and create a summary

🔧 Tool: shell
✅ Success (Auto-approved: Low risk)

🔧 Tool: file_operations
✅ Success (Auto-approved: Low risk)
```

### Risk Assessment

The system analyzes commands for various risk factors:

- **🟢 Low Risk**: `ls`, `pwd`, `cat`, `grep`, `find` (read-only)
- **🟡 Medium Risk**: `npm install`, `git commit`, file modifications
- **🟠 High Risk**: `sudo` commands, system modifications
- **🔴 Critical Risk**: `rm -rf /`, `format`, `fdisk`

## 🔧 Advanced Usage

### Environment Variables

```bash
# Set provider via environment
export KRITRIMA_PROVIDER_TYPE=openai
export KRITRIMA_API_KEY=your-key-here
export KRITRIMA_MODEL=gpt-4-turbo-preview

# Run with environment config
node src/index.js chat "Hello world"
```

### Custom Configuration File

```bash
# Use custom config file
node src/index.js --config /path/to/custom-config.yaml chat "Hello"
```

### Verbose Mode

```bash
# Enable verbose logging
node src/index.js --verbose chat "Debug this issue"
```

### Safe Mode

```bash
# Enable maximum safety restrictions
node src/index.js --safe-mode chat "Perform system maintenance"
```

## 🐛 Troubleshooting

### Common Issues

#### API Key Issues
```bash
# Check configuration
node src/index.js config --show

# Reconfigure
node src/index.js config --init

# Test connection
node src/index.js health
```

#### Permission Errors
```bash
# Check file permissions
ls -la ~/.kritrima/

# Fix permissions
chmod 755 ~/.kritrima/
chmod 644 ~/.kritrima/config.yaml
```

#### Network Issues
```bash
# Test connectivity
curl -I https://api.openai.com/v1/models

# Check proxy settings
echo $HTTP_PROXY
echo $HTTPS_PROXY
```

### Debug Mode

```bash
# Enable debug output
export KRITRIMA_DEBUG=true
node src/index.js chat "Debug this"
```

### Log Analysis

```bash
# View error logs
cat ~/.kritrima/error.log

# Monitor logs in real-time
tail -f ~/.kritrima/error.log
```

## 🎯 Best Practices

### 1. Start with Suggest Mode
Always begin with `suggest` mode to understand what the AI wants to do.

### 2. Review Commands
Always review shell commands before approval, especially those with:
- `sudo` or elevated privileges
- File deletion operations
- Network operations
- Package installations

### 3. Use Sessions
Leverage session management for complex, multi-step tasks.

### 4. Regular Health Checks
Run `node src/index.js health` regularly to ensure system health.

### 5. Backup Important Data
Always backup important files before running destructive operations.

### 6. Monitor Logs
Check error logs periodically for issues and patterns.

## 🚀 Integration Examples

### CI/CD Pipeline

```yaml
# .github/workflows/kritrima.yml
name: AI-Assisted Code Review
on: [pull_request]

jobs:
  ai-review:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install Kritrima CLI
        run: npm install -g kritrima-ai-cli
      - name: AI Code Review
        run: |
          kritrima config --set provider.apiKey=${{ secrets.OPENAI_API_KEY }}
          kritrima chat "Review this pull request and suggest improvements"
```

### Development Workflow

```bash
#!/bin/bash
# dev-workflow.sh

# Start development session
echo "Starting AI-assisted development session..."

# Analyze project
node src/index.js analyze --deep

# Start interactive session
node src/index.js chat
```

This comprehensive guide should help you get the most out of the Kritrima AI CLI system!
