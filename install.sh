#!/bin/bash

# Kritrima AI CLI Installation Script
# Production-ready agentic AI-powered CLI tool system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Emojis
ROCKET="🚀"
CHECK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"
GEAR="⚙️"
ROBOT="🤖"

echo -e "${CYAN}${ROBOT} Kritrima AI CLI Installation${NC}"
echo -e "${CYAN}Production-ready agentic AI-powered CLI tool system${NC}"
echo ""

# Check if Node.js is installed
check_nodejs() {
    echo -e "${INFO} Checking Node.js installation..."
    
    if ! command -v node &> /dev/null; then
        echo -e "${CROSS} Node.js is not installed"
        echo -e "${YELLOW}Please install Node.js 18+ from https://nodejs.org/${NC}"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1)
    
    if [ "$MAJOR_VERSION" -lt 18 ]; then
        echo -e "${CROSS} Node.js version $NODE_VERSION is too old"
        echo -e "${YELLOW}Please upgrade to Node.js 18+ from https://nodejs.org/${NC}"
        exit 1
    fi
    
    echo -e "${CHECK} Node.js $NODE_VERSION detected"
}

# Check if npm is installed
check_npm() {
    echo -e "${INFO} Checking npm installation..."
    
    if ! command -v npm &> /dev/null; then
        echo -e "${CROSS} npm is not installed"
        echo -e "${YELLOW}Please install npm or use Node.js installer from https://nodejs.org/${NC}"
        exit 1
    fi
    
    NPM_VERSION=$(npm --version)
    echo -e "${CHECK} npm $NPM_VERSION detected"
}

# Install dependencies
install_dependencies() {
    echo -e "${INFO} Installing dependencies..."
    
    if [ -f "package.json" ]; then
        npm install
        echo -e "${CHECK} Dependencies installed successfully"
    else
        echo -e "${CROSS} package.json not found"
        echo -e "${YELLOW}Please run this script from the Kritrima AI CLI directory${NC}"
        exit 1
    fi
}

# Make CLI executable
make_executable() {
    echo -e "${INFO} Making CLI executable..."
    
    if [ -f "src/index.js" ]; then
        chmod +x src/index.js
        echo -e "${CHECK} CLI made executable"
    else
        echo -e "${CROSS} src/index.js not found"
        exit 1
    fi
}

# Create symlink for global access (optional)
create_global_link() {
    echo -e "${INFO} Setting up global access..."
    
    read -p "Do you want to install Kritrima CLI globally? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if npm install -g .; then
            echo -e "${CHECK} Kritrima CLI installed globally"
            echo -e "${INFO} You can now use 'kritrima' command from anywhere"
        else
            echo -e "${WARNING} Global installation failed. You may need sudo privileges."
            echo -e "${INFO} You can still use: node src/index.js"
        fi
    else
        echo -e "${INFO} Skipping global installation"
        echo -e "${INFO} Use: node src/index.js to run the CLI"
    fi
}

# Test installation
test_installation() {
    echo -e "${INFO} Testing installation..."
    
    if node src/index.js --version &> /dev/null; then
        echo -e "${CHECK} Installation test passed"
    else
        echo -e "${CROSS} Installation test failed"
        exit 1
    fi
}

# Setup configuration
setup_configuration() {
    echo -e "${INFO} Setting up configuration..."
    
    read -p "Do you want to configure Kritrima CLI now? (Y/n): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        echo -e "${GEAR} Starting configuration wizard..."
        node src/index.js config --init
    else
        echo -e "${INFO} Skipping configuration. You can run 'kritrima config --init' later."
    fi
}

# Show completion message
show_completion() {
    echo ""
    echo -e "${GREEN}${ROCKET} Installation completed successfully!${NC}"
    echo ""
    echo -e "${CYAN}Quick Start:${NC}"
    echo -e "  ${YELLOW}# Configure the CLI${NC}"
    echo -e "  node src/index.js config --init"
    echo ""
    echo -e "  ${YELLOW}# Check system health${NC}"
    echo -e "  node src/index.js health"
    echo ""
    echo -e "  ${YELLOW}# Start interactive session${NC}"
    echo -e "  node src/index.js chat"
    echo ""
    echo -e "  ${YELLOW}# Analyze current project${NC}"
    echo -e "  node src/index.js analyze"
    echo ""
    echo -e "${CYAN}Documentation:${NC}"
    echo -e "  • README.md - Complete documentation"
    echo -e "  • USAGE_EXAMPLES.md - Usage examples"
    echo -e "  • .env.example - Environment configuration"
    echo ""
    echo -e "${CYAN}Support:${NC}"
    echo -e "  • GitHub: https://github.com/kritrima/ai-cli"
    echo -e "  • Issues: https://github.com/kritrima/ai-cli/issues"
    echo ""
    echo -e "${GREEN}Happy coding with AI assistance! ${ROBOT}${NC}"
}

# Main installation flow
main() {
    echo -e "${ROCKET} Starting installation process..."
    echo ""
    
    check_nodejs
    check_npm
    install_dependencies
    make_executable
    test_installation
    create_global_link
    setup_configuration
    show_completion
}

# Run installation
main "$@"
