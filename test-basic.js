#!/usr/bin/env node

/**
 * Basic functionality test for Kritrima AI CLI
 */

import { ProjectAnalyzer } from './src/context/ProjectAnalyzer.js';
import { ShellTool } from './src/tools/ShellTool.js';
import { FileTool } from './src/tools/FileTool.js';
import { ConfigManager } from './src/config/ConfigManager.js';

async function testBasicFunctionality() {
  console.log('🧪 Testing Kritrima AI CLI Basic Functionality\n');

  try {
    // Test 1: Configuration Management
    console.log('1. Testing Configuration Management...');
    const configManager = new ConfigManager();
    const config = await configManager.load();
    console.log('✅ Configuration loaded successfully');
    console.log(`   Provider: ${config.provider.type}`);
    console.log(`   Model: ${config.provider.model}\n`);

    // Test 2: Project Analysis
    console.log('2. Testing Project Analysis...');
    const projectAnalyzer = new ProjectAnalyzer();
    const analysis = await projectAnalyzer.analyze(process.cwd(), { maxFiles: 50 });
    console.log('✅ Project analysis completed');
    console.log(`   Files found: ${analysis.files.length}`);
    console.log(`   Project type: ${analysis.projectType || 'Unknown'}`);
    console.log(`   Dependencies: ${analysis.dependencies.length}\n`);

    // Test 3: Shell Tool
    console.log('3. Testing Shell Tool...');
    const shellTool = new ShellTool();
    const shellResult = await shellTool.execute({ command: 'echo "Hello from Kritrima CLI"' });
    if (shellResult.success) {
      console.log('✅ Shell tool working');
      console.log(`   Output: ${shellResult.stdout.trim()}\n`);
    } else {
      console.log('❌ Shell tool failed');
      console.log(`   Error: ${shellResult.error}\n`);
    }

    // Test 4: File Tool
    console.log('4. Testing File Tool...');
    const fileTool = new FileTool();
    
    // Test file creation
    const createResult = await fileTool.execute({
      operation: 'create',
      path: 'test-file.txt',
      content: 'Hello from Kritrima AI CLI!\nThis is a test file.'
    });
    
    if (createResult.success) {
      console.log('✅ File creation working');
      
      // Test file reading
      const readResult = await fileTool.execute({
        operation: 'read',
        path: 'test-file.txt'
      });
      
      if (readResult.success) {
        console.log('✅ File reading working');
        console.log(`   Content: ${readResult.result.content.split('\n')[0]}`);
        
        // Clean up test file
        await fileTool.execute({
          operation: 'delete',
          path: 'test-file.txt'
        });
        console.log('✅ File cleanup completed\n');
      } else {
        console.log('❌ File reading failed\n');
      }
    } else {
      console.log('❌ File creation failed');
      console.log(`   Error: ${createResult.error}\n`);
    }

    // Test 5: Health Checks
    console.log('5. Testing Health Checks...');
    const shellHealth = await shellTool.healthCheck();
    const projectHealth = await projectAnalyzer.healthCheck();
    
    console.log(`✅ Shell tool health: ${shellHealth.status}`);
    console.log(`✅ Project analyzer health: ${projectHealth.status}\n`);

    console.log('🎉 All basic functionality tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Configuration management');
    console.log('   ✅ Project analysis');
    console.log('   ✅ Shell command execution');
    console.log('   ✅ File operations');
    console.log('   ✅ Health monitoring');
    console.log('\n🚀 Kritrima AI CLI is ready for use!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('\nStack trace:');
    console.error(error.stack);
    process.exit(1);
  }
}

// Run tests
testBasicFunctionality();
