# Kritrima AI CLI Installation Script for Windows
# Production-ready agentic AI-powered CLI tool system

param(
    [switch]$Global,
    [switch]$SkipConfig
)

# Colors and emojis
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
    Magenta = "Magenta"
}

function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = "White"
    )
    Write-Host $Text -ForegroundColor $Color
}

function Write-Header {
    Write-ColorText "🤖 Kritrima AI CLI Installation" "Cyan"
    Write-ColorText "Production-ready agentic AI-powered CLI tool system" "Cyan"
    Write-Host ""
}

function Test-NodeJS {
    Write-ColorText "ℹ️  Checking Node.js installation..." "Blue"
    
    try {
        $nodeVersion = node --version 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "Node.js not found"
        }
        
        $versionNumber = $nodeVersion -replace 'v', ''
        $majorVersion = [int]($versionNumber -split '\.')[0]
        
        if ($majorVersion -lt 18) {
            Write-ColorText "❌ Node.js version $nodeVersion is too old" "Red"
            Write-ColorText "⚠️  Please upgrade to Node.js 18+ from https://nodejs.org/" "Yellow"
            exit 1
        }
        
        Write-ColorText "✅ Node.js $nodeVersion detected" "Green"
        return $true
    }
    catch {
        Write-ColorText "❌ Node.js is not installed" "Red"
        Write-ColorText "⚠️  Please install Node.js 18+ from https://nodejs.org/" "Yellow"
        exit 1
    }
}

function Test-NPM {
    Write-ColorText "ℹ️  Checking npm installation..." "Blue"
    
    try {
        $npmVersion = npm --version 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "npm not found"
        }
        
        Write-ColorText "✅ npm $npmVersion detected" "Green"
        return $true
    }
    catch {
        Write-ColorText "❌ npm is not installed" "Red"
        Write-ColorText "⚠️  Please install npm or use Node.js installer from https://nodejs.org/" "Yellow"
        exit 1
    }
}

function Install-Dependencies {
    Write-ColorText "ℹ️  Installing dependencies..." "Blue"
    
    if (Test-Path "package.json") {
        try {
            npm install
            if ($LASTEXITCODE -eq 0) {
                Write-ColorText "✅ Dependencies installed successfully" "Green"
            } else {
                throw "npm install failed"
            }
        }
        catch {
            Write-ColorText "❌ Failed to install dependencies" "Red"
            exit 1
        }
    } else {
        Write-ColorText "❌ package.json not found" "Red"
        Write-ColorText "⚠️  Please run this script from the Kritrima AI CLI directory" "Yellow"
        exit 1
    }
}

function Test-Installation {
    Write-ColorText "ℹ️  Testing installation..." "Blue"
    
    try {
        node src/index.js --version 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-ColorText "✅ Installation test passed" "Green"
        } else {
            throw "Installation test failed"
        }
    }
    catch {
        Write-ColorText "❌ Installation test failed" "Red"
        exit 1
    }
}

function Install-GlobalLink {
    Write-ColorText "ℹ️  Setting up global access..." "Blue"
    
    if ($Global) {
        $installGlobally = $true
    } else {
        $response = Read-Host "Do you want to install Kritrima CLI globally? (y/N)"
        $installGlobally = $response -match '^[Yy]$'
    }
    
    if ($installGlobally) {
        try {
            npm install -g .
            if ($LASTEXITCODE -eq 0) {
                Write-ColorText "✅ Kritrima CLI installed globally" "Green"
                Write-ColorText "ℹ️  You can now use 'kritrima' command from anywhere" "Blue"
            } else {
                throw "Global installation failed"
            }
        }
        catch {
            Write-ColorText "⚠️  Global installation failed. You may need administrator privileges." "Yellow"
            Write-ColorText "ℹ️  You can still use: node src/index.js" "Blue"
        }
    } else {
        Write-ColorText "ℹ️  Skipping global installation" "Blue"
        Write-ColorText "ℹ️  Use: node src/index.js to run the CLI" "Blue"
    }
}

function Setup-Configuration {
    Write-ColorText "ℹ️  Setting up configuration..." "Blue"
    
    if ($SkipConfig) {
        $configureNow = $false
    } else {
        $response = Read-Host "Do you want to configure Kritrima CLI now? (Y/n)"
        $configureNow = -not ($response -match '^[Nn]$')
    }
    
    if ($configureNow) {
        Write-ColorText "⚙️  Starting configuration wizard..." "Magenta"
        node src/index.js config --init
    } else {
        Write-ColorText "ℹ️  Skipping configuration. You can run 'kritrima config --init' later." "Blue"
    }
}

function Show-Completion {
    Write-Host ""
    Write-ColorText "🚀 Installation completed successfully!" "Green"
    Write-Host ""
    Write-ColorText "Quick Start:" "Cyan"
    Write-ColorText "  # Configure the CLI" "Yellow"
    Write-ColorText "  node src/index.js config --init" "White"
    Write-Host ""
    Write-ColorText "  # Check system health" "Yellow"
    Write-ColorText "  node src/index.js health" "White"
    Write-Host ""
    Write-ColorText "  # Start interactive session" "Yellow"
    Write-ColorText "  node src/index.js chat" "White"
    Write-Host ""
    Write-ColorText "  # Analyze current project" "Yellow"
    Write-ColorText "  node src/index.js analyze" "White"
    Write-Host ""
    Write-ColorText "Documentation:" "Cyan"
    Write-ColorText "  • README.md - Complete documentation" "White"
    Write-ColorText "  • USAGE_EXAMPLES.md - Usage examples" "White"
    Write-ColorText "  • .env.example - Environment configuration" "White"
    Write-Host ""
    Write-ColorText "Support:" "Cyan"
    Write-ColorText "  • GitHub: https://github.com/kritrima/ai-cli" "White"
    Write-ColorText "  • Issues: https://github.com/kritrima/ai-cli/issues" "White"
    Write-Host ""
    Write-ColorText "Happy coding with AI assistance! 🤖" "Green"
}

function Main {
    Write-Header
    Write-ColorText "🚀 Starting installation process..." "Magenta"
    Write-Host ""
    
    Test-NodeJS
    Test-NPM
    Install-Dependencies
    Test-Installation
    Install-GlobalLink
    Setup-Configuration
    Show-Completion
}

# Run installation
try {
    Main
}
catch {
    Write-ColorText "❌ Installation failed: $($_.Exception.Message)" "Red"
    exit 1
}
