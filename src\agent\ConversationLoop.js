/**
 * ConversationLoop - Manages the conversation flow between user, AI, and tools
 * Handles streaming responses, tool calls, and conversation state
 */

import { nanoid } from 'nanoid';

export class ConversationLoop {
  constructor(provider, toolRegistry, approvalSystem, streamProcessor, ui) {
    this.provider = provider;
    this.toolRegistry = toolRegistry;
    this.approvalSystem = approvalSystem;
    this.streamProcessor = streamProcessor;
    this.ui = ui;
    
    this.isRunning = false;
    this.currentSession = null;
    this.context = null;
  }

  /**
   * Start the interactive conversation loop
   */
  async start(session, context) {
    this.currentSession = session;
    this.context = context;
    this.isRunning = true;

    this.ui.info('Type your message or command. Use /help for available commands, /exit to quit.');

    while (this.isRunning) {
      try {
        const userInput = await this.ui.getUserInput();
        
        // Handle special commands
        if (userInput.startsWith('/')) {
          await this.handleSpecialCommand(userInput);
          continue;
        }

        // Process regular message
        await this.processMessage(userInput, this.currentSession, this.context);

      } catch (error) {
        if (error.name === 'UserInterruptError') {
          this.ui.info('\nGoodbye!');
          break;
        }
        this.ui.error(`Error: ${error.message}`);
      }
    }
  }

  /**
   * Process a single message through the conversation loop
   */
  async processMessage(message, session, context) {
    try {
      // Add user message to session
      const userMessage = {
        id: nanoid(),
        role: 'user',
        content: message,
        timestamp: new Date().toISOString()
      };
      session.messages.push(userMessage);

      // Prepare messages for AI
      const messages = this.prepareMessages(session, context);

      // Get available tools
      const tools = this.toolRegistry.getToolsForProvider(this.provider.type);

      // Stream AI response
      this.ui.startAIResponse();
      
      const stream = await this.provider.createChatCompletion({
        messages,
        tools,
        stream: true
      });

      let assistantMessage = {
        id: nanoid(),
        role: 'assistant',
        content: '',
        tool_calls: [],
        timestamp: new Date().toISOString()
      };

      // Process streaming response
      for await (const chunk of stream) {
        const processed = this.streamProcessor.processChunk(chunk);
        
        if (processed.content) {
          assistantMessage.content += processed.content;
          this.ui.appendToAIResponse(processed.content);
        }
        
        if (processed.tool_calls) {
          assistantMessage.tool_calls.push(...processed.tool_calls);
        }
      }

      this.ui.endAIResponse();

      // Add assistant message to session
      session.messages.push(assistantMessage);

      // Execute tool calls if any
      if (assistantMessage.tool_calls && assistantMessage.tool_calls.length > 0) {
        await this.executeToolCalls(assistantMessage.tool_calls, session);
      }

      return assistantMessage;

    } catch (error) {
      this.ui.error(`Failed to process message: ${error.message}`);
      throw error;
    }
  }

  /**
   * Execute tool calls with approval system
   */
  async executeToolCalls(toolCalls, session) {
    for (const toolCall of toolCalls) {
      try {
        // Parse tool call
        const { name, arguments: toolArgs } = toolCall.function;
        const parsedArgs = JSON.parse(toolArgs);

        // Get approval for tool execution
        const approved = await this.approvalSystem.requestApproval({
          tool: name,
          arguments: parsedArgs,
          context: this.context
        });

        if (!approved) {
          this.ui.warning(`Tool call ${name} was not approved`);
          continue;
        }

        // Execute tool
        this.ui.info(`Executing: ${name}`);
        const tool = this.toolRegistry.getTool(name);
        const result = await tool.execute(parsedArgs, this.context);

        // Add tool result to session
        const toolMessage = {
          id: nanoid(),
          role: 'tool',
          tool_call_id: toolCall.id,
          name: name,
          content: JSON.stringify(result),
          timestamp: new Date().toISOString()
        };
        session.messages.push(toolMessage);

        // Display result
        this.ui.displayToolResult(name, result);

        // If tool execution changed context, update it
        if (result.contextChanged) {
          await this.updateContext();
        }

      } catch (error) {
        this.ui.error(`Tool execution failed: ${error.message}`);
        
        // Add error message to session
        const errorMessage = {
          id: nanoid(),
          role: 'tool',
          tool_call_id: toolCall.id,
          name: toolCall.function.name,
          content: JSON.stringify({ error: error.message }),
          timestamp: new Date().toISOString()
        };
        session.messages.push(errorMessage);
      }
    }
  }

  /**
   * Handle special commands (starting with /)
   */
  async handleSpecialCommand(command) {
    const [cmd, ...args] = command.slice(1).split(' ');

    switch (cmd) {
      case 'help':
        this.ui.showHelp();
        break;
        
      case 'exit':
      case 'quit':
        this.isRunning = false;
        break;
        
      case 'clear':
        this.ui.clear();
        break;
        
      case 'context':
        this.ui.displayContext(this.context);
        break;
        
      case 'tools':
        const tools = this.toolRegistry.getAvailableTools();
        this.ui.displayTools(tools);
        break;
        
      case 'mode':
        if (args[0]) {
          this.approvalSystem.setMode(args[0]);
          this.ui.success(`Approval mode set to: ${args[0]}`);
        } else {
          this.ui.info(`Current approval mode: ${this.approvalSystem.getMode()}`);
        }
        break;
        
      case 'session':
        this.ui.displaySession(this.currentSession);
        break;
        
      case 'save':
        // Session is auto-saved, but this forces a save
        this.ui.success('Session saved');
        break;
        
      default:
        this.ui.error(`Unknown command: /${cmd}`);
        this.ui.info('Use /help to see available commands');
    }
  }

  /**
   * Prepare messages for AI provider
   */
  prepareMessages(session, context) {
    const messages = [];

    // Add system message with context
    if (context) {
      messages.push({
        role: 'system',
        content: this.buildSystemMessage(context)
      });
    }

    // Add conversation history
    messages.push(...session.messages.map(msg => ({
      role: msg.role,
      content: msg.content,
      tool_calls: msg.tool_calls,
      tool_call_id: msg.tool_call_id,
      name: msg.name
    })));

    return messages;
  }

  /**
   * Build system message with context information
   */
  buildSystemMessage(context) {
    let systemMessage = `You are Kritrima AI, an advanced coding assistant with access to shell commands and file operations.

Current working directory: ${context.workingDirectory}
Project type: ${context.projectType || 'Unknown'}
`;

    if (context.files && context.files.length > 0) {
      systemMessage += `\nProject files (${context.files.length} total):\n`;
      systemMessage += context.files.slice(0, 20).map(f => `- ${f.path}`).join('\n');
      if (context.files.length > 20) {
        systemMessage += `\n... and ${context.files.length - 20} more files`;
      }
    }

    if (context.dependencies && context.dependencies.length > 0) {
      systemMessage += `\nDependencies: ${context.dependencies.join(', ')}`;
    }

    systemMessage += `\n\nYou can execute shell commands, read/write files, and perform various operations.
Always explain what you're doing and ask for confirmation for potentially destructive operations.
Use the available tools to help the user with their coding tasks.`;

    return systemMessage;
  }

  /**
   * Update context after tool execution
   */
  async updateContext() {
    // This would trigger a context refresh
    // Implementation depends on the specific context analyzer
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    this.isRunning = false;
  }
}
